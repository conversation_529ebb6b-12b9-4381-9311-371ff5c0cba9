const { TokenPriceFetcher, ProtocolManager } = require('../index');

async function basicExample() {
    const priceFetcher = new TokenPriceFetcher();
    const protocolManager = new ProtocolManager();

    console.log('=== 基本使用示例 ===\n');

    try {
        // 1. 获取比特币价格
        console.log('1. 获取比特币价格:');
        const bitcoinPrice = await priceFetcher.getTokenPriceFromCoinGecko('bitcoin');
        console.log(`比特币价格: $${bitcoinPrice.price}`);
        console.log(`24小时变化: ${bitcoinPrice.change24h?.toFixed(2)}%\n`);

        // 2. 获取以太坊上的USDT价格
        console.log('2. 获取以太坊上的USDT价格:');
        const usdtAddress = protocolManager.getTokenAddress('ethereum', 'USDT');
        const usdtPrice = await priceFetcher.getTokenPriceFromCoinGecko(usdtAddress, 'ethereum');
        console.log(`USDT价格: $${usdtPrice.price}`);
        console.log(`合约地址: ${usdtAddress}\n`);

        // 3. 获取多个代币价格
        console.log('3. 获取多个代币价格:');
        const tokens = [
            { id: 'bitcoin', protocol: 'ethereum' },
            { id: 'ethereum', protocol: 'ethereum' },
            { id: protocolManager.getTokenAddress('binance-smart-chain', 'USDT'), protocol: 'binance-smart-chain' }
        ];
        
        const multiPrices = await priceFetcher.getMultipleTokenPrices(tokens);
        multiPrices.forEach(result => {
            if (result.success) {
                console.log(`${result.token} (${result.protocol}): $${result.data.price}`);
            } else {
                console.log(`${result.token} (${result.protocol}): 获取失败 - ${result.error}`);
            }
        });

        // 4. 列出支持的协议
        console.log('\n4. 支持的协议:');
        const protocols = protocolManager.getAllProtocols();
        protocols.forEach(protocol => {
            console.log(`- ${protocol.name} (${protocol.id}) - Chain ID: ${protocol.chainId}`);
        });

    } catch (error) {
        console.error('错误:', error.message);
    }
}

// 运行示例
if (require.main === module) {
    basicExample();
}
