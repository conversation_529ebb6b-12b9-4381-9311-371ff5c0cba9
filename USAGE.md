# 快速使用指南

## 立即开始

### 1. 查看支持的协议
```bash
node cli.js protocols
```

### 2. 获取协议详细信息
```bash
node cli.js protocol-info ethereum
node cli.js protocol-info binance-smart-chain
```

### 3. 获取代币合约地址
```bash
node cli.js address USDT ethereum
node cli.js address USDC polygon
```

### 4. 获取代币价格（需要网络连接）
```bash
# 获取比特币价格
node cli.js price bitcoin

# 获取以太坊价格
node cli.js price ethereum

# 获取特定协议上的代币价格
node cli.js price ****************************************** --protocol ethereum

# 指定货币单位
node cli.js price bitcoin --currency cny
```

### 5. 批量获取价格（需要网络连接）
```bash
node cli.js multi "bitcoin,ethereum,chainlink"
```

## 编程使用

### 基本示例
```javascript
const { TokenPriceFetcher, ProtocolManager } = require('./index');

async function example() {
    const protocolManager = new ProtocolManager();
    
    // 获取USDT在以太坊上的合约地址
    const usdtAddress = protocolManager.getTokenAddress('ethereum', 'USDT');
    console.log('USDT地址:', usdtAddress);
    
    // 获取协议信息
    const ethInfo = protocolManager.getProtocol('ethereum');
    console.log('以太坊信息:', ethInfo.name, ethInfo.chainId);
}

example();
```

## 测试

运行本地功能测试：
```bash
node test/local-test.js
```

运行示例（需要网络连接）：
```bash
node examples/basic-usage.js
node examples/advanced-usage.js
```

## 注意事项

1. **网络连接**: 价格获取功能需要互联网连接
2. **API限制**: 免费API有请求限制，请适度使用
3. **数据准确性**: 价格数据仅供参考，投资需谨慎

## 支持的协议

- Ethereum (ethereum)
- Binance Smart Chain (binance-smart-chain)  
- Polygon (polygon)
- Arbitrum One (arbitrum)
- Optimism (optimism)
- Avalanche C-Chain (avalanche)

## 支持的数据源

- CoinGecko (免费，无需API密钥)
- CoinMarketCap (需要API密钥)

## 环境配置

复制 `.env.example` 到 `.env` 并配置API密钥（可选）：
```bash
cp .env.example .env
```
