require('dotenv').config();

const config = {
    // API配置
    api: {
        coinmarketcap: {
            apiKey: process.env.COINMARKETCAP_API_KEY,
            baseUrl: 'https://pro-api.coinmarketcap.com/v1',
            timeout: parseInt(process.env.REQUEST_TIMEOUT) || 10000,
            rateLimit: 333 // 免费版每日请求限制
        },
        coingecko: {
            apiKey: process.env.COINGECKO_API_KEY,
            baseUrl: 'https://api.coingecko.com/api/v3',
            timeout: parseInt(process.env.REQUEST_TIMEOUT) || 10000,
            rateLimit: 50 // 免费版每分钟请求限制
        }
    },

    // 默认设置
    defaults: {
        currency: process.env.DEFAULT_CURRENCY || 'usd',
        protocol: process.env.DEFAULT_PROTOCOL || 'ethereum',
        apiSource: process.env.DEFAULT_API_SOURCE || 'coingecko',
        rateLimitDelay: parseInt(process.env.RATE_LIMIT_DELAY) || 200
    },

    // 支持的货币
    supportedCurrencies: [
        'usd', 'eur', 'jpy', 'gbp', 'aud', 'cad', 'chf', 'cny', 'sek', 'nzd',
        'mxn', 'sgd', 'hkd', 'nok', 'php', 'dkk', 'pln', 'rub', 'zar', 'brl',
        'btc', 'eth'
    ],

    // 日志配置
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || 'logs/token-price.log'
    },

    // 缓存配置
    cache: {
        enabled: true,
        ttl: 60000, // 1分钟缓存
        maxSize: 1000
    },

    // 重试配置
    retry: {
        maxAttempts: 3,
        delay: 1000,
        backoff: 2
    }
};

module.exports = config;
