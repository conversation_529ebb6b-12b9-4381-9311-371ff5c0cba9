const axios = require('axios');

class ProtocolManager {
    constructor() {
        this.protocols = {
            ethereum: {
                name: 'Ethereum',
                chainId: 1,
                nativeCurrency: {
                    name: 'Ether',
                    symbol: 'ETH',
                    decimals: 18
                },
                rpcUrls: [
                    'https://mainnet.infura.io/v3/',
                    'https://eth-mainnet.alchemyapi.io/v2/',
                    'https://cloudflare-eth.com'
                ],
                blockExplorerUrls: ['https://etherscan.io'],
                coingeckoId: 'ethereum',
                commonTokens: {
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'WETH': '******************************************',
                    'UNI': '******************************************'
                }
            },
            'binance-smart-chain': {
                name: 'Binance Smart Chain',
                chainId: 56,
                nativeCurrency: {
                    name: 'Binance Coin',
                    symbol: 'BNB',
                    decimals: 18
                },
                rpcUrls: [
                    'https://bsc-dataseed1.binance.org',
                    'https://bsc-dataseed2.binance.org',
                    'https://bsc-dataseed3.binance.org'
                ],
                blockExplorerUrls: ['https://bscscan.com'],
                coingeckoId: 'binance-smart-chain',
                commonTokens: {
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'BUSD': '0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56',
                    'CAKE': '0x0E09FaBB73Bd3Ade0a17ECC321fD13a19e81cE82'
                }
            },
            polygon: {
                name: 'Polygon',
                chainId: 137,
                nativeCurrency: {
                    name: 'MATIC',
                    symbol: 'MATIC',
                    decimals: 18
                },
                rpcUrls: [
                    'https://polygon-rpc.com',
                    'https://rpc-mainnet.matic.network',
                    'https://matic-mainnet.chainstacklabs.com'
                ],
                blockExplorerUrls: ['https://polygonscan.com'],
                coingeckoId: 'polygon-pos',
                commonTokens: {
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'WMATIC': '******************************************',
                    'QUICK': '******************************************'
                }
            },
            arbitrum: {
                name: 'Arbitrum One',
                chainId: 42161,
                nativeCurrency: {
                    name: 'Ether',
                    symbol: 'ETH',
                    decimals: 18
                },
                rpcUrls: [
                    'https://arb1.arbitrum.io/rpc',
                    'https://arbitrum-mainnet.infura.io/v3/'
                ],
                blockExplorerUrls: ['https://arbiscan.io'],
                coingeckoId: 'arbitrum-one',
                commonTokens: {
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'WETH': '******************************************',
                    'ARB': '******************************************'
                }
            },
            optimism: {
                name: 'Optimism',
                chainId: 10,
                nativeCurrency: {
                    name: 'Ether',
                    symbol: 'ETH',
                    decimals: 18
                },
                rpcUrls: [
                    'https://mainnet.optimism.io',
                    'https://optimism-mainnet.infura.io/v3/'
                ],
                blockExplorerUrls: ['https://optimistic.etherscan.io'],
                coingeckoId: 'optimistic-ethereum',
                commonTokens: {
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'WETH': '******************************************',
                    'OP': '******************************************'
                }
            },
            avalanche: {
                name: 'Avalanche C-Chain',
                chainId: 43114,
                nativeCurrency: {
                    name: 'Avalanche',
                    symbol: 'AVAX',
                    decimals: 18
                },
                rpcUrls: [
                    'https://api.avax.network/ext/bc/C/rpc',
                    'https://rpc.ankr.com/avalanche'
                ],
                blockExplorerUrls: ['https://snowtrace.io'],
                coingeckoId: 'avalanche',
                commonTokens: {
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'WAVAX': '******************************************',
                    'JOE': '0x6e84a6216eA6dACC71eE8E6b0a5B7322EEbC0fDd'
                }
            }
        };
    }

    /**
     * 获取协议信息
     * @param {string} protocolName - 协议名称
     */
    getProtocol(protocolName) {
        const protocol = this.protocols[protocolName.toLowerCase()];
        if (!protocol) {
            throw new Error(`不支持的协议: ${protocolName}`);
        }
        return protocol;
    }

    /**
     * 获取所有支持的协议
     */
    getAllProtocols() {
        return Object.keys(this.protocols).map(key => ({
            id: key,
            ...this.protocols[key]
        }));
    }

    /**
     * 根据链ID获取协议
     * @param {number} chainId - 链ID
     */
    getProtocolByChainId(chainId) {
        const protocolEntry = Object.entries(this.protocols).find(
            ([key, protocol]) => protocol.chainId === chainId
        );
        
        if (!protocolEntry) {
            throw new Error(`未找到链ID为 ${chainId} 的协议`);
        }
        
        return {
            id: protocolEntry[0],
            ...protocolEntry[1]
        };
    }

    /**
     * 获取协议的常用代币
     * @param {string} protocolName - 协议名称
     */
    getCommonTokens(protocolName) {
        const protocol = this.getProtocol(protocolName);
        return protocol.commonTokens;
    }

    /**
     * 获取代币合约地址
     * @param {string} protocolName - 协议名称
     * @param {string} tokenSymbol - 代币符号
     */
    getTokenAddress(protocolName, tokenSymbol) {
        const protocol = this.getProtocol(protocolName);
        const address = protocol.commonTokens[tokenSymbol.toUpperCase()];
        
        if (!address) {
            throw new Error(`在 ${protocolName} 上未找到 ${tokenSymbol} 的合约地址`);
        }
        
        return address;
    }

    /**
     * 验证合约地址格式
     * @param {string} address - 合约地址
     */
    isValidAddress(address) {
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }

    /**
     * 获取协议的区块浏览器URL
     * @param {string} protocolName - 协议名称
     * @param {string} address - 地址或交易哈希
     * @param {string} type - 类型 ('address' | 'tx' | 'token')
     */
    getExplorerUrl(protocolName, address, type = 'address') {
        const protocol = this.getProtocol(protocolName);
        const baseUrl = protocol.blockExplorerUrls[0];
        
        switch (type) {
            case 'address':
                return `${baseUrl}/address/${address}`;
            case 'tx':
                return `${baseUrl}/tx/${address}`;
            case 'token':
                return `${baseUrl}/token/${address}`;
            default:
                return `${baseUrl}/address/${address}`;
        }
    }

    /**
     * 检查协议是否支持
     * @param {string} protocolName - 协议名称
     */
    isProtocolSupported(protocolName) {
        return protocolName.toLowerCase() in this.protocols;
    }

    /**
     * 获取协议统计信息
     */
    getProtocolStats() {
        const protocols = this.getAllProtocols();
        return {
            totalProtocols: protocols.length,
            protocols: protocols.map(p => ({
                id: p.id,
                name: p.name,
                chainId: p.chainId,
                nativeCurrency: p.nativeCurrency.symbol,
                tokenCount: Object.keys(p.commonTokens).length
            }))
        };
    }
}

module.exports = ProtocolManager;
