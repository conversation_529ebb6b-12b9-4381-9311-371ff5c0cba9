const { TokenPriceFetcher, ProtocolManager } = require('../index');

function testLocalFunctionality() {
    console.log('=== 本地功能测试 ===\n');

    try {
        // 测试ProtocolManager
        const protocolManager = new ProtocolManager();
        
        console.log('1. 测试协议管理器:');
        const protocols = protocolManager.getAllProtocols();
        console.log(`✓ 支持 ${protocols.length} 个协议`);
        
        // 测试获取协议信息
        const ethProtocol = protocolManager.getProtocol('ethereum');
        console.log(`✓ 以太坊协议: ${ethProtocol.name}, Chain ID: ${ethProtocol.chainId}`);
        
        // 测试获取代币地址
        const usdtAddress = protocolManager.getTokenAddress('ethereum', 'USDT');
        console.log(`✓ USDT在以太坊上的地址: ${usdtAddress}`);
        
        // 测试地址验证
        const isValidAddress = protocolManager.isValidAddress(usdtAddress);
        console.log(`✓ 地址验证: ${isValidAddress}`);
        
        // 测试区块浏览器URL
        const explorerUrl = protocolManager.getExplorerUrl('ethereum', usdtAddress, 'token');
        console.log(`✓ 区块浏览器URL: ${explorerUrl}`);
        
        console.log('\n2. 测试协议统计:');
        const stats = protocolManager.getProtocolStats();
        console.log(`✓ 协议统计: ${stats.totalProtocols} 个协议`);
        stats.protocols.forEach(p => {
            console.log(`  - ${p.name}: ${p.tokenCount} 个代币`);
        });
        
        console.log('\n3. 测试TokenPriceFetcher初始化:');
        const priceFetcher = new TokenPriceFetcher();
        const supportedProtocols = priceFetcher.getSupportedProtocols();
        console.log(`✓ TokenPriceFetcher支持 ${supportedProtocols.length} 个协议`);
        
        console.log('\n4. 测试协议映射:');
        supportedProtocols.forEach(protocol => {
            console.log(`  - ${protocol}: CoinGecko ID存在`);
        });
        
        console.log('\n✅ 所有本地功能测试通过！');
        console.log('\n注意: 网络API功能需要互联网连接才能测试。');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testLocalFunctionality();
}
