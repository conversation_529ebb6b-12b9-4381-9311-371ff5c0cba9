const axios = require('axios');

async function testNetworkConnection() {
    console.log('=== 网络连接测试 ===\n');

    // 测试基本网络连接
    console.log('1. 测试基本网络连接...');
    try {
        const response = await axios.get('https://httpbin.org/ip', { timeout: 5000 });
        console.log('✅ 网络连接正常');
        console.log('   你的IP:', response.data.origin);
    } catch (error) {
        console.log('❌ 基本网络连接失败:', error.message);
        return;
    }

    // 测试CoinGecko API连接
    console.log('\n2. 测试CoinGecko API连接...');
    try {
        const response = await axios.get('https://api.coingecko.com/api/v3/ping', { 
            timeout: 10000,
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });
        console.log('✅ CoinGecko API连接正常');
        console.log('   响应:', response.data);
    } catch (error) {
        console.log('❌ CoinGecko API连接失败:', error.message);
        if (error.code === 'ENOTFOUND') {
            console.log('   可能的原因: DNS解析失败或网络被阻止');
        } else if (error.code === 'ETIMEDOUT') {
            console.log('   可能的原因: 网络超时，可能需要代理');
        }
    }

    // 测试简单的价格查询
    console.log('\n3. 测试简单的价格查询...');
    try {
        const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin&vs_currencies=usd', {
            timeout: 15000,
            headers: {
                'Accept': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });
        console.log('✅ 价格查询成功');
        console.log('   比特币价格:', response.data.bitcoin.usd, 'USD');
    } catch (error) {
        console.log('❌ 价格查询失败:', error.message);
        
        // 提供解决方案建议
        console.log('\n🔧 可能的解决方案:');
        console.log('1. 检查网络连接是否正常');
        console.log('2. 如果在中国大陆，可能需要使用代理或VPN');
        console.log('3. 检查防火墙设置');
        console.log('4. 尝试使用手机热点测试');
        console.log('5. 考虑使用国内的加密货币API服务');
    }

    // 测试备用API
    console.log('\n4. 测试备用数据源...');
    const backupApis = [
        'https://api.coinbase.com/v2/exchange-rates?currency=BTC',
        'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT'
    ];

    for (const api of backupApis) {
        try {
            const response = await axios.get(api, { timeout: 10000 });
            console.log(`✅ 备用API可用: ${api}`);
            break;
        } catch (error) {
            console.log(`❌ 备用API失败: ${api}`);
        }
    }
}

// 运行测试
if (require.main === module) {
    testNetworkConnection().catch(console.error);
}
