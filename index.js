const TokenPriceFetcher = require('./src/tokenPriceFetcher');
const ProtocolManager = require('./src/protocolManager');
const config = require('./config/config');

// 导出主要类和配置
module.exports = {
    TokenPriceFetcher,
    ProtocolManager,
    config
};

// 如果直接运行此文件，显示使用示例
if (require.main === module) {
    console.log('Token Price Fetcher - 代币价格获取工具');
    console.log('');
    console.log('使用方法:');
    console.log('  node cli.js price <token> [options]     - 获取单个代币价格');
    console.log('  node cli.js multi <tokens> [options]    - 获取多个代币价格');
    console.log('  node cli.js protocols                   - 列出支持的协议');
    console.log('  node cli.js protocol-info <protocol>    - 获取协议信息');
    console.log('  node cli.js address <token> <protocol>  - 获取代币合约地址');
    console.log('');
    console.log('示例:');
    console.log('  node cli.js price bitcoin');
    console.log('  node cli.js price ****************************************** --protocol ethereum');
    console.log('  node cli.js multi "bitcoin:ethereum,ethereum:ethereum,usdt:binance-smart-chain"');
    console.log('  node cli.js protocols');
    console.log('  node cli.js address USDT ethereum');
    console.log('');
    console.log('更多信息请运行: node cli.js --help');
}
