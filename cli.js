#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const TokenPriceFetcher = require('./src/tokenPriceFetcher');
const ProtocolManager = require('./src/protocolManager');
const config = require('./config/config');

const program = new Command();
const priceFetcher = new TokenPriceFetcher();
const protocolManager = new ProtocolManager();

program
    .name('token-price')
    .description('获取不同协议上代币的价值')
    .version('1.0.0');

// 获取单个代币价格
program
    .command('price')
    .description('获取代币价格')
    .argument('<token>', '代币ID或合约地址')
    .option('-p, --protocol <protocol>', '协议名称', config.defaults.protocol)
    .option('-c, --currency <currency>', '对比货币', config.defaults.currency)
    .option('-s, --source <source>', 'API源 (coingecko|coinmarketcap)', config.defaults.apiSource)
    .action(async (token, options) => {
        try {
            console.log(chalk.blue(`正在获取 ${token} 在 ${options.protocol} 上的价格...`));
            
            let result;
            if (options.source === 'coinmarketcap') {
                result = await priceFetcher.getTokenPriceFromCoinMarketCap(token, options.currency);
            } else {
                result = await priceFetcher.getTokenPriceFromCoinGecko(token, options.protocol, options.currency);
            }
            
            displayTokenPrice(token, result, options.protocol);
        } catch (error) {
            console.error(chalk.red(`错误: ${error.message}`));
            process.exit(1);
        }
    });

// 获取多个代币价格
program
    .command('multi')
    .description('获取多个代币价格')
    .argument('<tokens>', '代币列表，格式: token1:protocol1,token2:protocol2')
    .option('-c, --currency <currency>', '对比货币', config.defaults.currency)
    .option('-s, --source <source>', 'API源 (coingecko|coinmarketcap)', config.defaults.apiSource)
    .action(async (tokensStr, options) => {
        try {
            const tokens = parseTokenList(tokensStr);
            console.log(chalk.blue(`正在获取 ${tokens.length} 个代币的价格...`));
            
            const results = await priceFetcher.getMultipleTokenPrices(tokens, options.currency, options.source);
            displayMultipleTokenPrices(results);
        } catch (error) {
            console.error(chalk.red(`错误: ${error.message}`));
            process.exit(1);
        }
    });

// 列出支持的协议
program
    .command('protocols')
    .description('列出支持的协议')
    .action(() => {
        const protocols = protocolManager.getAllProtocols();
        console.log(chalk.green('\n支持的协议:'));
        console.log('─'.repeat(80));
        
        protocols.forEach(protocol => {
            console.log(chalk.cyan(`${protocol.id.padEnd(20)} ${protocol.name.padEnd(25)} Chain ID: ${protocol.chainId}`));
        });
        
        console.log('─'.repeat(80));
        console.log(chalk.yellow(`总计: ${protocols.length} 个协议`));
    });

// 获取协议信息
program
    .command('protocol-info')
    .description('获取协议详细信息')
    .argument('<protocol>', '协议名称')
    .action((protocolName) => {
        try {
            const protocol = protocolManager.getProtocol(protocolName);
            const commonTokens = protocolManager.getCommonTokens(protocolName);
            
            console.log(chalk.green(`\n${protocol.name} 协议信息:`));
            console.log('─'.repeat(50));
            console.log(`链ID: ${protocol.chainId}`);
            console.log(`原生货币: ${protocol.nativeCurrency.name} (${protocol.nativeCurrency.symbol})`);
            console.log(`区块浏览器: ${protocol.blockExplorerUrls[0]}`);
            
            console.log(chalk.green('\n常用代币:'));
            Object.entries(commonTokens).forEach(([symbol, address]) => {
                console.log(`  ${symbol.padEnd(8)} ${address}`);
            });
        } catch (error) {
            console.error(chalk.red(`错误: ${error.message}`));
            process.exit(1);
        }
    });

// 获取代币合约地址
program
    .command('address')
    .description('获取代币在指定协议上的合约地址')
    .argument('<token>', '代币符号')
    .argument('<protocol>', '协议名称')
    .action((token, protocol) => {
        try {
            const address = protocolManager.getTokenAddress(protocol, token);
            const explorerUrl = protocolManager.getExplorerUrl(protocol, address, 'token');
            
            console.log(chalk.green(`\n${token.toUpperCase()} 在 ${protocol} 上的合约地址:`));
            console.log(chalk.cyan(address));
            console.log(chalk.blue(`区块浏览器: ${explorerUrl}`));
        } catch (error) {
            console.error(chalk.red(`错误: ${error.message}`));
            process.exit(1);
        }
    });

// 辅助函数
function parseTokenList(tokensStr) {
    return tokensStr.split(',').map(tokenStr => {
        const [id, protocol] = tokenStr.split(':');
        return {
            id: id.trim(),
            protocol: protocol ? protocol.trim() : config.defaults.protocol
        };
    });
}

function displayTokenPrice(token, result, protocol) {
    console.log(chalk.green('\n价格信息:'));
    console.log('─'.repeat(50));
    console.log(`代币: ${chalk.cyan(token)}`);
    console.log(`协议: ${chalk.cyan(protocol)}`);
    console.log(`价格: ${chalk.yellow(result.price)} ${result.currency}`);
    
    if (result.marketCap) {
        console.log(`市值: ${chalk.yellow(formatNumber(result.marketCap))} ${result.currency}`);
    }
    
    if (result.change24h !== undefined) {
        const changeColor = result.change24h >= 0 ? chalk.green : chalk.red;
        console.log(`24h变化: ${changeColor(result.change24h.toFixed(2))}%`);
    }
    
    console.log(`数据源: ${chalk.blue(result.source)}`);
    console.log(`时间: ${chalk.gray(new Date(result.timestamp).toLocaleString())}`);
}

function displayMultipleTokenPrices(results) {
    console.log(chalk.green('\n代币价格列表:'));
    console.log('─'.repeat(100));
    console.log(chalk.cyan('代币'.padEnd(15) + '协议'.padEnd(20) + '价格'.padEnd(15) + '24h变化'.padEnd(12) + '状态'));
    console.log('─'.repeat(100));
    
    results.forEach(result => {
        if (result.success) {
            const changeColor = result.data.change24h >= 0 ? chalk.green : chalk.red;
            const change24h = result.data.change24h ? result.data.change24h.toFixed(2) + '%' : 'N/A';
            
            console.log(
                result.token.padEnd(15) +
                result.protocol.padEnd(20) +
                `${result.data.price} ${result.data.currency}`.padEnd(15) +
                changeColor(change24h.padEnd(12)) +
                chalk.green('✓')
            );
        } else {
            console.log(
                result.token.padEnd(15) +
                result.protocol.padEnd(20) +
                'N/A'.padEnd(15) +
                'N/A'.padEnd(12) +
                chalk.red('✗ ' + result.error)
            );
        }
    });
    
    console.log('─'.repeat(100));
    const successCount = results.filter(r => r.success).length;
    console.log(chalk.yellow(`成功: ${successCount}/${results.length}`));
}

function formatNumber(num) {
    if (num >= 1e9) {
        return (num / 1e9).toFixed(2) + 'B';
    } else if (num >= 1e6) {
        return (num / 1e6).toFixed(2) + 'M';
    } else if (num >= 1e3) {
        return (num / 1e3).toFixed(2) + 'K';
    }
    return num.toFixed(2);
}

program.parse();
