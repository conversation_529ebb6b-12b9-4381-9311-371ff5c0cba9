const { TokenPriceFetcher, ProtocolManager } = require('../index');

async function advancedExample() {
    const priceFetcher = new TokenPriceFetcher();
    const protocolManager = new ProtocolManager();

    console.log('=== 高级使用示例 ===\n');

    try {
        // 1. 跨协议代币价格比较
        console.log('1. 跨协议USDT价格比较:');
        const usdtComparison = await compareTokenAcrossProtocols('USDT', ['ethereum', 'binance-smart-chain', 'polygon']);
        usdtComparison.forEach(result => {
            if (result.success) {
                console.log(`${result.protocol}: $${result.data.price} (${result.data.source})`);
            } else {
                console.log(`${result.protocol}: 获取失败`);
            }
        });

        // 2. 获取协议原生代币价格
        console.log('\n2. 各协议原生代币价格:');
        const nativeTokens = [
            { id: 'ethereum', protocol: 'ethereum', name: 'ETH' },
            { id: 'binancecoin', protocol: 'binance-smart-chain', name: 'BNB' },
            { id: 'matic-network', protocol: 'polygon', name: 'MATIC' },
            { id: 'avalanche-2', protocol: 'avalanche', name: 'AVAX' }
        ];

        for (const token of nativeTokens) {
            try {
                const price = await priceFetcher.getTokenPriceFromCoinGecko(token.id);
                console.log(`${token.name}: $${price.price} (24h: ${price.change24h?.toFixed(2)}%)`);
            } catch (error) {
                console.log(`${token.name}: 获取失败`);
            }
            await new Promise(resolve => setTimeout(resolve, 300)); // 避免API限制
        }

        // 3. 获取DeFi代币价格
        console.log('\n3. 热门DeFi代币价格:');
        const defiTokens = [
            { id: 'uniswap', name: 'UNI' },
            { id: 'chainlink', name: 'LINK' },
            { id: 'aave', name: 'AAVE' },
            { id: 'compound-governance-token', name: 'COMP' }
        ];

        for (const token of defiTokens) {
            try {
                const price = await priceFetcher.getTokenPriceFromCoinGecko(token.id);
                console.log(`${token.name}: $${price.price} (市值: $${formatMarketCap(price.marketCap)})`);
            } catch (error) {
                console.log(`${token.name}: 获取失败`);
            }
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        // 4. 协议统计信息
        console.log('\n4. 协议统计信息:');
        const stats = protocolManager.getProtocolStats();
        console.log(`支持的协议总数: ${stats.totalProtocols}`);
        stats.protocols.forEach(protocol => {
            console.log(`- ${protocol.name}: ${protocol.tokenCount} 个常用代币`);
        });

    } catch (error) {
        console.error('错误:', error.message);
    }
}

// 跨协议代币价格比较
async function compareTokenAcrossProtocols(tokenSymbol, protocols) {
    const priceFetcher = new TokenPriceFetcher();
    const protocolManager = new ProtocolManager();
    const results = [];

    for (const protocol of protocols) {
        try {
            const address = protocolManager.getTokenAddress(protocol, tokenSymbol);
            const price = await priceFetcher.getTokenPriceFromCoinGecko(address, protocol);
            results.push({
                protocol,
                success: true,
                data: price
            });
        } catch (error) {
            results.push({
                protocol,
                success: false,
                error: error.message
            });
        }
        await new Promise(resolve => setTimeout(resolve, 300)); // 避免API限制
    }

    return results;
}

// 格式化市值
function formatMarketCap(marketCap) {
    if (!marketCap) return 'N/A';
    
    if (marketCap >= 1e9) {
        return (marketCap / 1e9).toFixed(2) + 'B';
    } else if (marketCap >= 1e6) {
        return (marketCap / 1e6).toFixed(2) + 'M';
    } else if (marketCap >= 1e3) {
        return (marketCap / 1e3).toFixed(2) + 'K';
    }
    return marketCap.toFixed(2);
}

// 运行示例
if (require.main === module) {
    advancedExample();
}
