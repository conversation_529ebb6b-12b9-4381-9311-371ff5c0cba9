require('dotenv').config();
const { TokenPriceFetcher } = require('../index');

async function testCoinMarketCapAPI() {
    console.log('=== CoinMarketCap API 测试 ===\n');

    // 检查API密钥
    const apiKey = process.env.COINMARKETCAP_API_KEY;
    if (!apiKey || apiKey === '请在这里输入你的CoinMarketCap_API密钥') {
        console.log('❌ 请先在 .env 文件中设置你的 COINMARKETCAP_API_KEY');
        console.log('   1. 编辑 .env 文件');
        console.log('   2. 将 COINMARKETCAP_API_KEY=请在这里输入你的CoinMarketCap_API密钥');
        console.log('   3. 替换为 COINMARKETCAP_API_KEY=你的实际密钥');
        return;
    }

    console.log('✅ API密钥已配置');
    console.log(`   密钥前缀: ${apiKey.substring(0, 8)}...`);

    const priceFetcher = new TokenPriceFetcher();

    // 测试获取比特币价格
    console.log('\n1. 测试获取比特币价格...');
    try {
        const btcPrice = await priceFetcher.getTokenPriceFromCoinMarketCap('BTC', 'USD');
        console.log('✅ 比特币价格获取成功:');
        console.log(`   价格: $${btcPrice.price}`);
        console.log(`   市值: $${formatNumber(btcPrice.marketCap)}`);
        console.log(`   24h变化: ${btcPrice.change24h?.toFixed(2)}%`);
        console.log(`   数据源: ${btcPrice.source}`);
    } catch (error) {
        console.log('❌ 比特币价格获取失败:', error.message);
        return;
    }

    // 测试获取以太坊价格
    console.log('\n2. 测试获取以太坊价格...');
    try {
        const ethPrice = await priceFetcher.getTokenPriceFromCoinMarketCap('ETH', 'USD');
        console.log('✅ 以太坊价格获取成功:');
        console.log(`   价格: $${ethPrice.price}`);
        console.log(`   24h变化: ${ethPrice.change24h?.toFixed(2)}%`);
    } catch (error) {
        console.log('❌ 以太坊价格获取失败:', error.message);
    }

    // 测试获取人民币价格
    console.log('\n3. 测试获取人民币价格...');
    try {
        const btcCnyPrice = await priceFetcher.getTokenPriceFromCoinMarketCap('BTC', 'CNY');
        console.log('✅ 比特币人民币价格获取成功:');
        console.log(`   价格: ¥${btcCnyPrice.price}`);
    } catch (error) {
        console.log('❌ 人民币价格获取失败:', error.message);
    }

    // 测试多个代币
    console.log('\n4. 测试多个热门代币...');
    const tokens = ['BTC', 'ETH', 'BNB', 'ADA', 'SOL'];
    
    for (const token of tokens) {
        try {
            await new Promise(resolve => setTimeout(resolve, 1000)); // 避免API限制
            const price = await priceFetcher.getTokenPriceFromCoinMarketCap(token, 'USD');
            console.log(`✅ ${token}: $${price.price} (${price.change24h?.toFixed(2)}%)`);
        } catch (error) {
            console.log(`❌ ${token}: 获取失败`);
        }
    }

    console.log('\n🎉 CoinMarketCap API测试完成！');
}

function formatNumber(num) {
    if (!num) return 'N/A';
    
    if (num >= 1e12) {
        return (num / 1e12).toFixed(2) + 'T';
    } else if (num >= 1e9) {
        return (num / 1e9).toFixed(2) + 'B';
    } else if (num >= 1e6) {
        return (num / 1e6).toFixed(2) + 'M';
    } else if (num >= 1e3) {
        return (num / 1e3).toFixed(2) + 'K';
    }
    return num.toFixed(2);
}

// 运行测试
if (require.main === module) {
    testCoinMarketCapAPI().catch(console.error);
}
