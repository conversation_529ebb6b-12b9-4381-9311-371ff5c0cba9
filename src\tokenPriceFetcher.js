const axios = require('axios');

class TokenPriceFetcher {
    constructor() {
        this.apiSources = {
            coingecko: {
                baseUrl: 'https://api.coingecko.com/api/v3',
                rateLimit: 50, // requests per minute for free tier
            },
            coinmarketcap: {
                baseUrl: 'https://pro-api.coinmarketcap.com/v1',
                rateLimit: 333, // requests per day for free tier
            }
        };
        
        // 协议到平台ID的映射
        this.protocolMapping = {
            ethereum: {
                coingecko: 'ethereum',
                coinmarketcap: 'ETH'
            },
            'binance-smart-chain': {
                coingecko: 'binance-smart-chain',
                coinmarketcap: 'BSC'
            },
            polygon: {
                coingecko: 'polygon-pos',
                coinmarketcap: 'MATIC'
            },
            arbitrum: {
                coingecko: 'arbitrum-one',
                coinmarketcap: 'ARBITRUM'
            },
            optimism: {
                coingecko: 'optimistic-ethereum',
                coinmarketcap: 'OPTIMISM'
            },
            avalanche: {
                coingecko: 'avalanche',
                coinmarketcap: 'AVAX'
            }
        };
    }

    /**
     * 从CoinGecko获取代币价格
     * @param {string} tokenId - 代币ID或合约地址
     * @param {string} protocol - 协议名称
     * @param {string} vsCurrency - 对比货币 (usd, cny等)
     */
    async getTokenPriceFromCoinGecko(tokenId, protocol = 'ethereum', vsCurrency = 'usd') {
        try {
            let url;
            
            // 如果是合约地址（以0x开头），使用合约地址查询
            if (tokenId.startsWith('0x')) {
                const platformId = this.protocolMapping[protocol]?.coingecko || protocol;
                url = `${this.apiSources.coingecko.baseUrl}/simple/token_price/${platformId}?contract_addresses=${tokenId}&vs_currencies=${vsCurrency}&include_market_cap=true&include_24hr_change=true`;
            } else {
                // 使用代币ID查询
                url = `${this.apiSources.coingecko.baseUrl}/simple/price?ids=${tokenId}&vs_currencies=${vsCurrency}&include_market_cap=true&include_24hr_change=true`;
            }

            const response = await axios.get(url, {
                timeout: 10000,
                headers: {
                    'Accept': 'application/json',
                }
            });

            return this.formatCoinGeckoResponse(response.data, tokenId, vsCurrency);
        } catch (error) {
            throw new Error(`CoinGecko API错误: ${error.message}`);
        }
    }

    /**
     * 从CoinMarketCap获取代币价格
     * @param {string} symbol - 代币符号
     * @param {string} convert - 转换货币
     */
    async getTokenPriceFromCoinMarketCap(symbol, convert = 'USD') {
        try {
            const apiKey = process.env.COINMARKETCAP_API_KEY;
            if (!apiKey) {
                throw new Error('需要设置COINMARKETCAP_API_KEY环境变量');
            }

            const url = `${this.apiSources.coinmarketcap.baseUrl}/cryptocurrency/quotes/latest`;
            const response = await axios.get(url, {
                params: {
                    symbol: symbol.toUpperCase(),
                    convert: convert.toUpperCase()
                },
                headers: {
                    'X-CMC_PRO_API_KEY': apiKey,
                    'Accept': 'application/json',
                },
                timeout: 10000
            });

            return this.formatCoinMarketCapResponse(response.data, symbol, convert);
        } catch (error) {
            throw new Error(`CoinMarketCap API错误: ${error.message}`);
        }
    }

    /**
     * 获取多个代币的价格
     * @param {Array} tokens - 代币列表 [{id: 'bitcoin', protocol: 'ethereum'}, ...]
     * @param {string} vsCurrency - 对比货币
     * @param {string} source - API源 ('coingecko' | 'coinmarketcap' | 'auto')
     */
    async getMultipleTokenPrices(tokens, vsCurrency = 'usd', source = 'auto') {
        const results = [];
        
        for (const token of tokens) {
            try {
                let price;
                
                if (source === 'auto' || source === 'coingecko') {
                    price = await this.getTokenPriceFromCoinGecko(
                        token.id, 
                        token.protocol || 'ethereum', 
                        vsCurrency
                    );
                } else if (source === 'coinmarketcap') {
                    price = await this.getTokenPriceFromCoinMarketCap(
                        token.symbol || token.id, 
                        vsCurrency
                    );
                }
                
                results.push({
                    token: token.id,
                    protocol: token.protocol || 'ethereum',
                    success: true,
                    data: price
                });
                
                // 添加延迟以避免触发API限制
                await this.delay(200);
                
            } catch (error) {
                results.push({
                    token: token.id,
                    protocol: token.protocol || 'ethereum',
                    success: false,
                    error: error.message
                });
            }
        }
        
        return results;
    }

    /**
     * 格式化CoinGecko响应
     */
    formatCoinGeckoResponse(data, tokenId, vsCurrency) {
        const key = tokenId.startsWith('0x') ? tokenId.toLowerCase() : tokenId;
        const tokenData = data[key];
        
        if (!tokenData) {
            throw new Error('未找到代币数据');
        }

        return {
            price: tokenData[vsCurrency],
            marketCap: tokenData[`${vsCurrency}_market_cap`],
            change24h: tokenData[`${vsCurrency}_24h_change`],
            currency: vsCurrency.toUpperCase(),
            source: 'CoinGecko',
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 格式化CoinMarketCap响应
     */
    formatCoinMarketCapResponse(data, symbol, convert) {
        const tokenData = data.data[symbol.toUpperCase()];
        
        if (!tokenData) {
            throw new Error('未找到代币数据');
        }

        const quote = tokenData.quote[convert.toUpperCase()];
        
        return {
            price: quote.price,
            marketCap: quote.market_cap,
            change24h: quote.percent_change_24h,
            currency: convert.toUpperCase(),
            source: 'CoinMarketCap',
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 获取支持的协议列表
     */
    getSupportedProtocols() {
        return Object.keys(this.protocolMapping);
    }
}

module.exports = TokenPriceFetcher;
