# Token Price Fetcher - 代币价格获取工具

一个强大的Node.js工具，用于获取不同区块链协议上代币的实时价格信息。支持多个数据源和协议，包括以太坊、BSC、Polygon、Arbitrum等。

## 特性

- 🚀 支持多个区块链协议（以太坊、BSC、Polygon、Arbitrum、Optimism、Avalanche）
- 📊 多个数据源支持（CoinGecko、CoinMarketCap）
- 💰 实时价格、市值和24小时变化数据
- 🔧 易用的命令行界面
- 📦 可作为npm包使用
- ⚡ 内置API限制处理
- 🌍 支持多种法币和加密货币计价

## 安装

```bash
# 克隆项目
git clone <repository-url>
cd 协议代币

# 安装依赖
npm install
```

## 配置

1. 复制环境变量示例文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，添加API密钥（可选）：
```env
# CoinMarketCap API密钥 (可选)
COINMARKETCAP_API_KEY=your_api_key_here

# CoinGecko Pro API密钥 (可选，免费版本无需密钥)
COINGECKO_API_KEY=your_api_key_here
```

## 使用方法

### 命令行界面

#### 获取单个代币价格
```bash
# 获取比特币价格
node cli.js price bitcoin

# 获取以太坊上的代币价格（使用合约地址）
node cli.js price ****************************************** --protocol ethereum

# 指定货币单位
node cli.js price ethereum --currency cny

# 使用CoinMarketCap数据源
node cli.js price BTC --source coinmarketcap
```

#### 获取多个代币价格
```bash
# 获取多个代币价格
node cli.js multi "bitcoin:ethereum,ethereum:ethereum,usdt:binance-smart-chain"

# 指定货币单位
node cli.js multi "bitcoin,ethereum" --currency eur
```

#### 查看支持的协议
```bash
# 列出所有支持的协议
node cli.js protocols

# 获取特定协议信息
node cli.js protocol-info ethereum
```

#### 获取代币合约地址
```bash
# 获取USDT在以太坊上的合约地址
node cli.js address USDT ethereum

# 获取USDC在Polygon上的合约地址
node cli.js address USDC polygon
```

### 编程接口

#### 基本使用
```javascript
const { TokenPriceFetcher, ProtocolManager } = require('./index');

const priceFetcher = new TokenPriceFetcher();
const protocolManager = new ProtocolManager();

// 获取比特币价格
const bitcoinPrice = await priceFetcher.getTokenPriceFromCoinGecko('bitcoin');
console.log(`比特币价格: $${bitcoinPrice.price}`);

// 获取以太坊上USDT的价格
const usdtAddress = protocolManager.getTokenAddress('ethereum', 'USDT');
const usdtPrice = await priceFetcher.getTokenPriceFromCoinGecko(usdtAddress, 'ethereum');
console.log(`USDT价格: $${usdtPrice.price}`);
```

#### 批量获取价格
```javascript
const tokens = [
    { id: 'bitcoin', protocol: 'ethereum' },
    { id: 'ethereum', protocol: 'ethereum' },
    { id: '0x...', protocol: 'binance-smart-chain' }
];

const results = await priceFetcher.getMultipleTokenPrices(tokens, 'usd', 'coingecko');
results.forEach(result => {
    if (result.success) {
        console.log(`${result.token}: $${result.data.price}`);
    }
});
```

## 支持的协议

| 协议名称 | ID | Chain ID | 原生代币 |
|---------|----|---------|---------| 
| Ethereum | ethereum | 1 | ETH |
| Binance Smart Chain | binance-smart-chain | 56 | BNB |
| Polygon | polygon | 137 | MATIC |
| Arbitrum One | arbitrum | 42161 | ETH |
| Optimism | optimism | 10 | ETH |
| Avalanche C-Chain | avalanche | 43114 | AVAX |

## 支持的货币

### 法币
USD, EUR, JPY, GBP, AUD, CAD, CHF, CNY, SEK, NZD, MXN, SGD, HKD, NOK, PHP, DKK, PLN, RUB, ZAR, BRL

### 加密货币
BTC, ETH

## API数据源

### CoinGecko
- **免费版本**: 50次/分钟
- **Pro版本**: 需要API密钥，更高限制
- **特点**: 支持合约地址查询，数据全面

### CoinMarketCap  
- **免费版本**: 333次/天，需要API密钥
- **付费版本**: 更高限制
- **特点**: 数据权威，更新及时

## 示例

查看 `examples/` 目录中的示例文件：

- `basic-usage.js` - 基本使用示例
- `advanced-usage.js` - 高级功能示例

运行示例：
```bash
node examples/basic-usage.js
node examples/advanced-usage.js
```

## 错误处理

工具内置了完善的错误处理机制：

- API请求超时处理
- 网络错误重试
- API限制检测
- 无效参数验证

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License

## 注意事项

1. 使用免费API时请注意请求限制
2. 价格数据仅供参考，投资需谨慎
3. 建议在生产环境中使用付费API以获得更好的稳定性
4. 某些代币可能在不同协议上有不同的价格
